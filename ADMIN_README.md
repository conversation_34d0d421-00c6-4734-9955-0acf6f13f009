# Luxe Fashion Admin Dashboard

## Overview
The Admin Dashboard provides a comprehensive interface for managing the Luxe Fashion e-commerce platform. This dashboard is accessible only to users with admin privileges.

## Access
- URL: `/admin.html`
- Default admin credentials:
  - Email: <EMAIL>
  - Password: admin123

## Features

### Dashboard Overview
- View key metrics: total users, orders, revenue, and products
- Monitor recent user registrations and orders
- Track monthly growth percentages

### User Management
- View all registered users
- Filter users by status (active/inactive)
- Search users by name or email
- Activate/deactivate user accounts
- Delete user accounts

### Product Management
- View all products in the catalog
- Add new products
- Edit existing product details
- Delete products

### Order Management
- View all customer orders
- Filter orders by status
- Update order status (processing, shipped, delivered, etc.)
- View order details

### Analytics
- View sales performance metrics
- Track user engagement statistics

### Settings
- Configure store settings and preferences

## Security Notes
- The admin dashboard is protected by authentication
- Only users with the 'admin' role can access the dashboard
- Unauthorized access attempts are redirected to the homepage

## Development Notes
- Admin functionality is implemented in the admin.html file
- Admin-specific JavaScript functions are in script.js
- User authentication and admin checks are handled by the auth object