<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Orders - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="products.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Products
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <a href="wishlist.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </a>
                    </div>
                    <div class="relative">
                        <a href="cart.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </a>
                    </div>

                    <!-- User Menu -->
                    <div class="hidden lg:flex items-center space-x-3 user-menu" style="display: none;">
                        <div class="relative group">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-primary font-medium transition duration-300">
                                <i class="fas fa-user-circle text-xl"></i>
                                <span class="user-name">User</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-2">
                                    <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">
                                        <i class="fas fa-user mr-2"></i>My Profile
                                    </a>
                                    <a href="orders.html" class="block px-4 py-2 text-sm text-primary font-medium hover:bg-purple-light">
                                        <i class="fas fa-shopping-bag mr-2"></i>My Orders
                                    </a>
                                    <a href="admin.html" class="admin-link block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary" style="display: none;">
                                        <i class="fas fa-cog mr-2"></i>Admin Dashboard
                                    </a>
                                    <hr class="my-2">
                                    <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-primary font-medium">My Orders</span>
            </nav>

            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">My Orders</h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Track and manage your order history</p>
            </div>
        </div>
    </section>

    <!-- Orders Content -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Order Filters -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">Showing</span>
                    <span class="text-gray-600"><span id="orders-count">0</span> orders</span>
                </div>

                <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <select id="order-filter" class="appearance-none bg-white border border-gray-200 rounded-xl px-4 py-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                        <option value="all">All Orders</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
            </div>

            <!-- Orders List -->
            <div id="orders-container" class="space-y-6">
                <!-- Empty state will be shown here initially -->
                <div id="empty-orders" class="text-center py-16">
                    <div class="mb-8">
                        <i class="fas fa-shopping-bag text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-2xl font-semibold text-gray-600 mb-2">No orders yet</h3>
                        <p class="text-gray-500">Start shopping to see your orders here</p>
                    </div>
                    <a href="products.html" class="inline-flex items-center space-x-2 bg-primary text-white px-8 py-4 rounded-2xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Start Shopping</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-400">&copy; 2024 Luxe Fashion. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // Orders page specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            if (!auth.isAuthenticated()) {
                window.location.href = 'index.html';
                return;
            }

            loadUserOrders();
            setupOrdersEventListeners();
        });

        function loadUserOrders() {
            const userOrders = orders.filter(order => order.userId === auth.getCurrentUser().id);
            const ordersContainer = document.getElementById('orders-container');
            const emptyOrders = document.getElementById('empty-orders');
            const ordersCount = document.getElementById('orders-count');
            
            if (userOrders.length === 0) {
                emptyOrders.style.display = 'block';
                ordersCount.textContent = '0';
            } else {
                emptyOrders.style.display = 'none';
                ordersCount.textContent = userOrders.length;
                
                // Create order cards
                const ordersHTML = userOrders.map(order => createOrderCard(order)).join('');
                ordersContainer.innerHTML = ordersHTML + emptyOrders.outerHTML;
            }
        }

        function createOrderCard(order) {
            const statusColors = {
                pending: 'bg-yellow-100 text-yellow-800',
                processing: 'bg-blue-100 text-blue-800',
                shipped: 'bg-purple-100 text-purple-800',
                delivered: 'bg-green-100 text-green-800',
                cancelled: 'bg-red-100 text-red-800'
            };

            return `
                <div class="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Order #${order.id}</h3>
                            <p class="text-gray-600">Placed on ${new Date(order.createdAt).toLocaleDateString()}</p>
                        </div>
                        <div class="flex flex-col items-start lg:items-end space-y-2">
                            <span class="px-3 py-1 rounded-full text-sm font-medium ${statusColors[order.status] || 'bg-gray-100 text-gray-800'}">
                                ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </span>
                            <span class="text-2xl font-bold text-primary">$${order.total.toFixed(2)}</span>
                        </div>
                    </div>
                    
                    <div class="border-t pt-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                            ${order.items.slice(0, 3).map(item => `
                                <div class="flex items-center space-x-3">
                                    <div class="w-16 h-16 bg-gradient-to-br from-secondary to-purple-light rounded-lg flex-shrink-0"></div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">${item.name}</h4>
                                        <p class="text-sm text-gray-600">Qty: ${item.quantity}</p>
                                        <p class="text-sm font-semibold text-primary">$${item.price}</p>
                                    </div>
                                </div>
                            `).join('')}
                            ${order.items.length > 3 ? `
                                <div class="flex items-center justify-center text-gray-500">
                                    <span class="text-sm">+${order.items.length - 3} more items</span>
                                </div>
                            ` : ''}
                        </div>
                        
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
                            <div class="text-sm text-gray-600">
                                <p><strong>Items:</strong> ${order.items.length}</p>
                                <p><strong>Shipping:</strong> ${order.shippingAddress.city}, ${order.shippingAddress.state}</p>
                            </div>
                            <div class="flex space-x-3">
                                <button onclick="viewOrderDetails('${order.id}')" class="bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-accent transition-all duration-300">
                                    View Details
                                </button>
                                ${order.status === 'delivered' ? `
                                    <button onclick="reorderItems('${order.id}')" class="border border-primary text-primary px-4 py-2 rounded-lg font-medium hover:bg-primary hover:text-white transition-all duration-300">
                                        Reorder
                                    </button>
                                ` : ''}
                                ${order.status === 'pending' ? `
                                    <button onclick="cancelOrder('${order.id}')" class="border border-red-500 text-red-500 px-4 py-2 rounded-lg font-medium hover:bg-red-500 hover:text-white transition-all duration-300">
                                        Cancel
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function setupOrdersEventListeners() {
            document.getElementById('order-filter').addEventListener('change', function() {
                const filterValue = this.value;
                filterOrders(filterValue);
            });
        }

        function filterOrders(status) {
            const userOrders = orders.filter(order => order.userId === auth.getCurrentUser().id);
            let filteredOrders = userOrders;
            
            if (status !== 'all') {
                filteredOrders = userOrders.filter(order => order.status === status);
            }
            
            const ordersContainer = document.getElementById('orders-container');
            const emptyOrders = document.getElementById('empty-orders');
            const ordersCount = document.getElementById('orders-count');
            
            if (filteredOrders.length === 0) {
                emptyOrders.style.display = 'block';
                ordersCount.textContent = '0';
                ordersContainer.innerHTML = emptyOrders.outerHTML;
            } else {
                emptyOrders.style.display = 'none';
                ordersCount.textContent = filteredOrders.length;
                
                const ordersHTML = filteredOrders.map(order => createOrderCard(order)).join('');
                ordersContainer.innerHTML = ordersHTML + emptyOrders.outerHTML;
            }
        }

        function viewOrderDetails(orderId) {
            const order = orders.find(o => o.id === orderId);
            if (order) {
                // Create a modal or redirect to order details page
                alert(`Order Details:\n\nOrder ID: ${order.id}\nStatus: ${order.status}\nTotal: $${order.total.toFixed(2)}\nItems: ${order.items.length}\n\nThis would normally show a detailed view.`);
            }
        }

        function reorderItems(orderId) {
            const order = orders.find(o => o.id === orderId);
            if (order) {
                // Add all items from the order back to cart
                order.items.forEach(item => {
                    addToCart(item.id, item.name, item.price);
                });
                showNotification('Items added to cart!', 'success');
            }
        }

        function cancelOrder(orderId) {
            if (confirm('Are you sure you want to cancel this order?')) {
                const orderIndex = orders.findIndex(o => o.id === orderId);
                if (orderIndex !== -1) {
                    orders[orderIndex].status = 'cancelled';
                    auth.saveOrders();
                    loadUserOrders();
                    showNotification('Order cancelled successfully', 'info');
                }
            }
        }
    </script>
</body>
</html>
