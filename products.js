// Product data with categories
const productData = {
    dresses: [
        { id: 1, name: "Elegant Summer Dress", price: 89.99, rating: 4.8, image: "dress1.jpg", category: "dresses", badge: "New" },
        { id: 2, name: "Floral Maxi Dress", price: 129.99, rating: 4.9, image: "dress2.jpg", category: "dresses", badge: "Popular" },
        { id: 3, name: "Cocktail Party Dress", price: 159.99, rating: 4.7, image: "dress3.jpg", category: "dresses", badge: "" },
        { id: 4, name: "Casual Day Dress", price: 69.99, rating: 4.6, image: "dress4.jpg", category: "dresses", badge: "Sale" },
    ],
    tops: [
        { id: 5, name: "Silk Blouse", price: 79.99, rating: 4.5, image: "top1.jpg", category: "tops", badge: "New" },
        { id: 6, name: "Cotton T-Shirt", price: 29.99, rating: 4.4, image: "top2.jpg", category: "tops", badge: "" },
        { id: 7, name: "Designer Tank Top", price: 49.99, rating: 4.6, image: "top3.jpg", category: "tops", badge: "Popular" },
        { id: 8, name: "Casual Sweater", price: 89.99, rating: 4.7, image: "top4.jpg", category: "tops", badge: "" },
    ],
    bottoms: [
        { id: 9, name: "High-Waist Jeans", price: 99.99, rating: 4.8, image: "bottom1.jpg", category: "bottoms", badge: "Popular" },
        { id: 10, name: "Pleated Skirt", price: 69.99, rating: 4.5, image: "bottom2.jpg", category: "bottoms", badge: "" },
        { id: 11, name: "Yoga Pants", price: 59.99, rating: 4.6, image: "bottom3.jpg", category: "bottoms", badge: "New" },
        { id: 12, name: "Formal Trousers", price: 119.99, rating: 4.7, image: "bottom4.jpg", category: "bottoms", badge: "" },
    ],
    shoes: [
        { id: 13, name: "Designer Heels", price: 149.99, rating: 4.9, image: "shoe1.jpg", category: "shoes", badge: "Popular" },
        { id: 14, name: "Casual Sneakers", price: 89.99, rating: 4.6, image: "shoe2.jpg", category: "shoes", badge: "" },
        { id: 15, name: "Ankle Boots", price: 129.99, rating: 4.7, image: "shoe3.jpg", category: "shoes", badge: "New" },
        { id: 16, name: "Ballet Flats", price: 79.99, rating: 4.5, image: "shoe4.jpg", category: "shoes", badge: "" },
    ],
    bags: [
        { id: 17, name: "Leather Handbag", price: 199.99, rating: 4.8, image: "bag1.jpg", category: "bags", badge: "Popular" },
        { id: 18, name: "Crossbody Bag", price: 89.99, rating: 4.6, image: "bag2.jpg", category: "bags", badge: "" },
        { id: 19, name: "Evening Clutch", price: 69.99, rating: 4.5, image: "bag3.jpg", category: "bags", badge: "New" },
        { id: 20, name: "Tote Bag", price: 119.99, rating: 4.7, image: "bag4.jpg", category: "bags", badge: "" },
    ],
    shirts: [
        { id: 21, name: "Formal Dress Shirt", price: 79.99, rating: 4.7, image: "shirt1.jpg", category: "shirts", badge: "" },
        { id: 22, name: "Casual Button-Up", price: 59.99, rating: 4.5, image: "shirt2.jpg", category: "shirts", badge: "Popular" },
        { id: 23, name: "Polo Shirt", price: 49.99, rating: 4.6, image: "shirt3.jpg", category: "shirts", badge: "" },
        { id: 24, name: "Flannel Shirt", price: 69.99, rating: 4.4, image: "shirt4.jpg", category: "shirts", badge: "New" },
    ],
    pants: [
        { id: 25, name: "Chino Pants", price: 89.99, rating: 4.6, image: "pant1.jpg", category: "pants", badge: "" },
        { id: 26, name: "Denim Jeans", price: 99.99, rating: 4.8, image: "pant2.jpg", category: "pants", badge: "Popular" },
        { id: 27, name: "Dress Pants", price: 119.99, rating: 4.7, image: "pant3.jpg", category: "pants", badge: "" },
        { id: 28, name: "Cargo Pants", price: 79.99, rating: 4.5, image: "pant4.jpg", category: "pants", badge: "New" },
    ],
    jackets: [
        { id: 29, name: "Leather Jacket", price: 249.99, rating: 4.9, image: "jacket1.jpg", category: "jackets", badge: "Popular" },
        { id: 30, name: "Blazer", price: 159.99, rating: 4.7, image: "jacket2.jpg", category: "jackets", badge: "" },
        { id: 31, name: "Denim Jacket", price: 89.99, rating: 4.6, image: "jacket3.jpg", category: "jackets", badge: "New" },
        { id: 32, name: "Windbreaker", price: 69.99, rating: 4.5, image: "jacket4.jpg", category: "jackets", badge: "" },
    ],
    jewelry: [
        { id: 33, name: "Diamond Necklace", price: 299.99, rating: 4.9, image: "jewelry1.jpg", category: "jewelry", badge: "Popular" },
        { id: 34, name: "Gold Earrings", price: 149.99, rating: 4.8, image: "jewelry2.jpg", category: "jewelry", badge: "" },
        { id: 35, name: "Silver Bracelet", price: 89.99, rating: 4.6, image: "jewelry3.jpg", category: "jewelry", badge: "New" },
        { id: 36, name: "Fashion Ring", price: 59.99, rating: 4.5, image: "jewelry4.jpg", category: "jewelry", badge: "" },
    ],
    watches: [
        { id: 37, name: "Luxury Watch", price: 399.99, rating: 4.9, image: "watch1.jpg", category: "watches", badge: "Popular" },
        { id: 38, name: "Sport Watch", price: 199.99, rating: 4.7, image: "watch2.jpg", category: "watches", badge: "" },
        { id: 39, name: "Classic Watch", price: 249.99, rating: 4.8, image: "watch3.jpg", category: "watches", badge: "New" },
        { id: 40, name: "Smart Watch", price: 299.99, rating: 4.6, image: "watch4.jpg", category: "watches", badge: "" },
    ],
    sunglasses: [
        { id: 41, name: "Designer Sunglasses", price: 179.99, rating: 4.8, image: "sunglasses1.jpg", category: "sunglasses", badge: "Popular" },
        { id: 42, name: "Aviator Sunglasses", price: 129.99, rating: 4.6, image: "sunglasses2.jpg", category: "sunglasses", badge: "" },
        { id: 43, name: "Cat Eye Sunglasses", price: 99.99, rating: 4.7, image: "sunglasses3.jpg", category: "sunglasses", badge: "New" },
        { id: 44, name: "Sport Sunglasses", price: 89.99, rating: 4.5, image: "sunglasses4.jpg", category: "sunglasses", badge: "" },
    ],
    activewear: [
        { id: 45, name: "Yoga Set", price: 79.99, rating: 4.7, image: "active1.jpg", category: "activewear", badge: "Popular" },
        { id: 46, name: "Running Shorts", price: 39.99, rating: 4.5, image: "active2.jpg", category: "activewear", badge: "" },
        { id: 47, name: "Sports Bra", price: 49.99, rating: 4.6, image: "active3.jpg", category: "activewear", badge: "New" },
        { id: 48, name: "Track Suit", price: 99.99, rating: 4.8, image: "active4.jpg", category: "activewear", badge: "" },
    ],
    formal: [
        { id: 49, name: "Evening Gown", price: 299.99, rating: 4.9, image: "formal1.jpg", category: "formal", badge: "Popular" },
        { id: 50, name: "Tuxedo", price: 399.99, rating: 4.8, image: "formal2.jpg", category: "formal", badge: "" },
        { id: 51, name: "Cocktail Dress", price: 199.99, rating: 4.7, image: "formal3.jpg", category: "formal", badge: "New" },
        { id: 52, name: "Formal Suit", price: 349.99, rating: 4.8, image: "formal4.jpg", category: "formal", badge: "" },
    ]
};

// Category information
const categoryInfo = {
    dresses: { title: "Dresses", description: "Elegant and stylish dresses for every occasion" },
    tops: { title: "Tops", description: "Casual and chic tops to complete your look" },
    bottoms: { title: "Bottoms", description: "Comfortable and fashionable pants and skirts" },
    shoes: { title: "Women's Shoes", description: "Step out in style with our shoe collection" },
    bags: { title: "Bags", description: "Handbags and purses for the modern woman" },
    shirts: { title: "Shirts", description: "Classic and modern shirts for men" },
    pants: { title: "Pants", description: "Quality pants and jeans for every style" },
    jackets: { title: "Jackets", description: "Stylish outerwear for all seasons" },
    jewelry: { title: "Jewelry", description: "Beautiful jewelry to complement your style" },
    watches: { title: "Watches", description: "Luxury timepieces for the discerning individual" },
    sunglasses: { title: "Sunglasses", description: "Designer frames to protect and style" },
    activewear: { title: "Activewear", description: "Performance wear for your active lifestyle" },
    formal: { title: "Formal Wear", description: "Elegant attire for special occasions" }
};

// Get URL parameters
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// Get current category from URL
let currentCategory = getUrlParameter('category') || 'all';
let currentProducts = [];
let displayedProducts = 0;
const productsPerPage = 8;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCategoryProducts();
    updateCategoryHeader();
    setupEventListeners();
});

function loadCategoryProducts() {
    if (currentCategory === 'all') {
        // Load all products
        currentProducts = [];
        Object.values(productData).forEach(categoryProducts => {
            currentProducts = currentProducts.concat(categoryProducts);
        });
    } else {
        // Load specific category
        currentProducts = productData[currentCategory] || [];
    }
    
    displayedProducts = 0;
    document.getElementById('products-grid').innerHTML = '';
    loadMoreProducts();
}

function updateCategoryHeader() {
    const titleElement = document.getElementById('category-title');
    const descriptionElement = document.getElementById('category-description');
    const breadcrumbElement = document.getElementById('breadcrumb-category');
    
    if (currentCategory === 'all') {
        titleElement.textContent = 'All Products';
        descriptionElement.textContent = 'Discover our complete collection of premium fashion items';
        breadcrumbElement.textContent = 'All Products';
    } else {
        const info = categoryInfo[currentCategory];
        if (info) {
            titleElement.textContent = info.title;
            descriptionElement.textContent = info.description;
            breadcrumbElement.textContent = info.title;
        }
    }
}

function loadMoreProducts() {
    const grid = document.getElementById('products-grid');
    const loadMoreBtn = document.getElementById('load-more-btn');
    
    const nextProducts = currentProducts.slice(displayedProducts, displayedProducts + productsPerPage);
    
    nextProducts.forEach(product => {
        const productCard = createProductCard(product);
        grid.appendChild(productCard);
    });
    
    displayedProducts += nextProducts.length;
    
    // Update product count
    document.getElementById('product-count').textContent = displayedProducts;
    
    // Hide load more button if all products are displayed
    if (displayedProducts >= currentProducts.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
    }
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card group cursor-pointer';
    card.setAttribute('data-category', product.category);
    card.setAttribute('data-price', product.price);
    card.setAttribute('data-rating', product.rating);
    
    const badgeHtml = product.badge ? `<span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">${product.badge}</span>` : '';
    
    card.innerHTML = `
        <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
            <div class="relative h-72 bg-gradient-to-br from-secondary to-purple-light overflow-hidden">
                ${badgeHtml ? `<div class="absolute top-4 left-4 z-10">${badgeHtml}</div>` : ''}
                
                <div class="absolute top-4 right-4 z-10 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 wishlist-btn">
                        <i class="far fa-heart text-gray-600 hover:text-red-500 text-sm"></i>
                    </button>
                    <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 quick-view-btn">
                        <i class="fas fa-eye text-gray-600 hover:text-primary text-sm"></i>
                    </button>
                </div>
                
                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                    <button class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-accent transition-colors duration-300 add-to-cart-btn">
                        Add to Cart
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-xs text-gray-500 uppercase tracking-wide">${product.category}</span>
                    <div class="flex items-center space-x-1">
                        <div class="flex text-yellow-400">
                            ${generateStars(product.rating)}
                        </div>
                        <span class="text-xs text-gray-500">(${product.rating})</span>
                    </div>
                </div>
                <h4 class="font-semibold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">${product.name}</h4>
                <div class="flex items-center justify-between">
                    <span class="text-2xl font-bold text-primary">$${product.price}</span>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Free Shipping</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return card;
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHtml = '';
    
    for (let i = 0; i < fullStars; i++) {
        starsHtml += '<i class="fas fa-star text-xs"></i>';
    }
    
    if (hasHalfStar) {
        starsHtml += '<i class="fas fa-star-half-alt text-xs"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<i class="far fa-star text-xs"></i>';
    }
    
    return starsHtml;
}

function setupEventListeners() {
    // Load more button
    document.getElementById('load-more-btn').addEventListener('click', loadMoreProducts);
    
    // Sort functionality
    document.getElementById('sort-select').addEventListener('change', function() {
        const sortBy = this.value;
        sortProducts(sortBy);
    });
}

function sortProducts(sortBy) {
    switch (sortBy) {
        case 'price-low':
            currentProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            currentProducts.sort((a, b) => b.price - a.price);
            break;
        case 'rating':
            currentProducts.sort((a, b) => b.rating - a.rating);
            break;
        case 'newest':
            currentProducts.sort((a, b) => b.id - a.id);
            break;
        default:
            // Featured - keep original order
            break;
    }
    
    // Reload products with new sort order
    displayedProducts = 0;
    document.getElementById('products-grid').innerHTML = '';
    loadMoreProducts();
}
