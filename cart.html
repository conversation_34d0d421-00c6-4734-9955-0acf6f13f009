<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="products.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Products
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-md mx-8">
                    <div class="relative w-full">
                        <input type="text" placeholder="Search for products..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                               id="search-input">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <a href="wishlist.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </a>
                    </div>
                    <div class="relative">
                        <a href="cart.html" class="text-primary hover:text-accent cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </a>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="hidden lg:flex items-center space-x-3">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="lg:hidden hidden bg-white/95 backdrop-blur-xl border-t border-white/20 rounded-b-2xl">
            <div class="px-4 pt-4 pb-6 space-y-3">
                <!-- Mobile Search -->
                <div class="md:hidden mb-4">
                    <div class="relative">
                        <input type="text" placeholder="Search products..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <a href="index.html" class="block px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium">Home</a>
                <a href="products.html" class="block px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium">Products</a>

                <!-- Mobile Auth Buttons -->
                <div class="border-t border-gray-200 pt-4 mt-4 space-y-2">
                    <button onclick="openLoginModal()" class="w-full px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium text-left">
                        Login
                    </button>
                    <button onclick="openSignupModal()" class="w-full bg-primary text-white px-3 py-3 rounded-lg font-medium hover:bg-accent transition duration-300">
                        Sign Up
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-primary font-medium">Shopping Cart</span>
            </nav>

            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">Shopping Cart</h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Review your items and proceed to checkout</p>
            </div>
        </div>
    </section>

    <!-- Cart Content -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Cart Items -->
                <div class="lg:col-span-2">
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900">Cart Items</h2>
                        <span class="text-gray-600"><span id="cart-item-count">0</span> items</span>
                    </div>

                    <div id="cart-items-container" class="space-y-6">
                        <!-- Empty state will be shown here initially -->
                        <div id="empty-cart" class="text-center py-16">
                            <div class="mb-8">
                                <i class="fas fa-shopping-bag text-6xl text-gray-300 mb-4"></i>
                                <h3 class="text-2xl font-semibold text-gray-600 mb-2">Your cart is empty</h3>
                                <p class="text-gray-500">Add some items to your cart to get started</p>
                            </div>
                            <a href="products.html" class="inline-flex items-center space-x-2 bg-primary text-white px-8 py-4 rounded-2xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                                <i class="fas fa-shopping-bag"></i>
                                <span>Start Shopping</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-50 rounded-2xl p-8 sticky top-24">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Order Summary</h3>
                        
                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="font-semibold" id="cart-subtotal">$0.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Shipping</span>
                                <span class="font-semibold text-green-600">Free</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax</span>
                                <span class="font-semibold" id="cart-tax">$0.00</span>
                            </div>
                            <div class="border-t pt-4">
                                <div class="flex justify-between">
                                    <span class="text-lg font-bold">Total</span>
                                    <span class="text-lg font-bold text-primary" id="cart-final-total">$0.00</span>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <a href="checkout.html" id="checkout-btn" class="block w-full bg-primary text-white py-4 rounded-2xl font-semibold text-center hover:bg-accent transition-all duration-300 transform hover:scale-105">
                                Proceed to Checkout
                            </a>
                            <a href="products.html" class="block w-full border-2 border-primary text-primary py-4 rounded-2xl font-semibold text-center hover:bg-primary hover:text-white transition-all duration-300">
                                Continue Shopping
                            </a>
                        </div>

                        <!-- Promo Code -->
                        <div class="mt-8 pt-8 border-t">
                            <h4 class="font-semibold text-gray-900 mb-4">Promo Code</h4>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Enter code" class="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary">
                                <button class="bg-gray-200 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-300 transition-colors duration-300">
                                    Apply
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h4 class="text-xl font-bold text-secondary mb-4">Luxe Fashion</h4>
                    <p class="text-gray-400 mb-4">Your destination for premium fashion and timeless style.</p>
                    <div class="flex space-x-4">
                        <i class="fab fa-facebook text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-instagram text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-twitter text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                        <i class="fab fa-pinterest text-secondary hover:text-white cursor-pointer transition duration-300"></i>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Quick Links</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Size Guide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Returns</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Categories</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Women's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Men's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Accessories</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Sale Items</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Customer Service</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Shipping Info</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Track Order</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Luxe Fashion. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // Cart-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadCartItems();
            updateCartSummary();
        });

        function loadCartItems() {
            const cartContainer = document.getElementById('cart-items-container');
            const emptyCart = document.getElementById('empty-cart');
            const itemCount = document.getElementById('cart-item-count');
            
            if (cart.length === 0) {
                emptyCart.style.display = 'block';
                itemCount.textContent = '0';
            } else {
                emptyCart.style.display = 'none';
                itemCount.textContent = cart.length;
                
                // Create cart item cards
                const itemsHTML = cart.map(item => createCartItemCard(item)).join('');
                cartContainer.innerHTML = itemsHTML + emptyCart.outerHTML;
            }
        }

        function createCartItemCard(item) {
            return `
                <div class="bg-white border border-gray-200 rounded-2xl p-6 cart-item" data-id="${item.id}">
                    <div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
                        <div class="w-full md:w-32 h-32 bg-gradient-to-br from-secondary to-purple-light rounded-xl flex-shrink-0"></div>
                        
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">${item.name}</h3>
                            <p class="text-gray-600 mb-2">Size: M | Color: Blue</p>
                            <div class="flex items-center space-x-4">
                                <span class="text-2xl font-bold text-primary">$${item.price}</span>
                                <span class="text-sm text-green-600">In Stock</span>
                            </div>
                        </div>
                        
                        <div class="flex flex-col space-y-3 w-full md:w-auto">
                            <div class="flex items-center space-x-3">
                                <button onclick="updateQuantity('${item.id}', -1)" class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors duration-300">
                                    <i class="fas fa-minus text-sm"></i>
                                </button>
                                <span class="text-lg font-semibold w-8 text-center" id="qty-${item.id}">1</span>
                                <button onclick="updateQuantity('${item.id}', 1)" class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors duration-300">
                                    <i class="fas fa-plus text-sm"></i>
                                </button>
                            </div>
                            <button onclick="removeFromCartPage('${item.id}')" class="bg-red-500 text-white px-6 py-3 rounded-xl font-medium hover:bg-red-600 transition-all duration-300 transform hover:scale-105">
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function updateQuantity(itemId, change) {
            const qtyElement = document.getElementById(`qty-${itemId}`);
            let currentQty = parseInt(qtyElement.textContent);
            let newQty = Math.max(1, currentQty + change);
            
            qtyElement.textContent = newQty;
            updateCartSummary();
        }

        function removeFromCartPage(itemId) {
            removeFromCart(itemId);
            loadCartItems();
            updateCartSummary();
        }

        function updateCartSummary() {
            const subtotal = cart.reduce((sum, item) => sum + item.price, 0);
            const tax = subtotal * 0.08; // 8% tax
            const total = subtotal + tax;

            document.getElementById('cart-subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('cart-tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('cart-final-total').textContent = `$${total.toFixed(2)}`;
        }
    </script>
</body>
</html>
