<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-gray-50 text-gray-800 font-body">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <!-- Logo and Title -->
            <div class="flex items-center space-x-4">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-primary">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <div>
                    <h1 class="text-2xl font-bold text-primary font-display">Luxe Fashion</h1>
                    <p class="text-sm text-gray-600">Admin Dashboard</p>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="flex items-center space-x-4">
                <!-- Notifications -->
                <button class="relative text-gray-600 hover:text-primary">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>

                <!-- User Menu -->
                <div class="relative group">
                    <button class="flex items-center space-x-2 text-gray-700 hover:text-primary">
                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <span class="user-name font-medium">Admin</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                        <div class="py-2">
                            <a href="index.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light">
                                <i class="fas fa-home mr-2"></i>View Website
                            </a>
                            <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <hr class="my-2">
                            <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside id="sidebar" class="fixed top-16 left-0 h-full w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 z-40">
        <nav class="p-6">
            <ul class="space-y-2">
                <li>
                    <button onclick="showAdminSection('dashboard')" class="admin-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 bg-primary text-white">
                        <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                    </button>
                </li>
                <li>
                    <button onclick="showAdminSection('users')" class="admin-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                        <i class="fas fa-users mr-3"></i>Users
                    </button>
                </li>
                <li>
                    <button onclick="showAdminSection('products')" class="admin-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                        <i class="fas fa-box mr-3"></i>Products
                    </button>
                </li>
                <li>
                    <button onclick="showAdminSection('orders')" class="admin-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                        <i class="fas fa-shopping-cart mr-3"></i>Orders
                    </button>
                </li>
                <li>
                    <button onclick="showAdminSection('analytics')" class="admin-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                        <i class="fas fa-chart-bar mr-3"></i>Analytics
                    </button>
                </li>
                <li>
                    <button onclick="showAdminSection('settings')" class="admin-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                        <i class="fas fa-cog mr-3"></i>Settings
                    </button>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="lg:ml-64 pt-16 min-h-screen">
        <div class="p-6">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="admin-section">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Dashboard Overview</h2>
                    <p class="text-gray-600">Welcome back! Here's what's happening with your store.</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Users</p>
                                <p class="text-3xl font-bold text-gray-900" id="total-users">0</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+12% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Orders</p>
                                <p class="text-3xl font-bold text-gray-900" id="total-orders">0</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-shopping-cart text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+8% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Revenue</p>
                                <p class="text-3xl font-bold text-gray-900" id="total-revenue">$0</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+15% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Products</p>
                                <p class="text-3xl font-bold text-gray-900" id="total-products">0</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-box text-orange-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+5% from last month</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Recent Orders</h3>
                        <div id="recent-orders" class="space-y-4">
                            <!-- Recent orders will be populated here -->
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Recent Users</h3>
                        <div id="recent-users" class="space-y-4">
                            <!-- Recent users will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Section -->
            <div id="users-section" class="admin-section hidden">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">User Management</h2>
                    <p class="text-gray-600">Manage registered users and their accounts.</p>
                </div>

                <!-- Users Table -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                            <h3 class="text-xl font-semibold text-gray-900">All Users</h3>
                            <div class="flex space-x-3">
                                <input type="text" id="user-search" placeholder="Search users..." class="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <select id="user-filter" class="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="all">All Users</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="admin">Admins</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
                                <!-- Users will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div id="products-section" class="admin-section hidden">
                <div class="mb-8">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-900 mb-2">Product Management</h2>
                            <p class="text-gray-600">Add, edit, and manage your product catalog.</p>
                        </div>
                        <button onclick="openAddProductModal()" class="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-accent transition-all duration-300">
                            <i class="fas fa-plus mr-2"></i>Add Product
                        </button>
                    </div>
                </div>

                <!-- Products Grid -->
                <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!-- Products will be populated here -->
                </div>
            </div>

            <!-- Orders Section -->
            <div id="orders-section" class="admin-section hidden">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Order Management</h2>
                    <p class="text-gray-600">View and manage customer orders.</p>
                </div>

                <!-- Orders Table -->
                <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                            <h3 class="text-xl font-semibold text-gray-900">All Orders</h3>
                            <div class="flex space-x-3">
                                <input type="text" id="order-search" placeholder="Search orders..." class="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <select id="order-status-filter" class="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="all">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="orders-table-body" class="bg-white divide-y divide-gray-200">
                                <!-- Orders will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="admin-section hidden">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Analytics</h2>
                    <p class="text-gray-600">View detailed analytics and reports.</p>
                </div>

                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Sales Analytics</h3>
                    <p class="text-gray-600">Detailed analytics features would be implemented here with charts and graphs.</p>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="admin-section hidden">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Settings</h2>
                    <p class="text-gray-600">Configure your store settings and preferences.</p>
                </div>

                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Store Configuration</h3>
                    <p class="text-gray-600">Store settings and configuration options would be implemented here.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // Admin-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is admin
            if (!auth.isAuthenticated() || !auth.isAdminUser()) {
                window.location.href = 'index.html';
                return;
            }

            loadAdminDashboard();
            setupAdminEventListeners();
        });

        function loadAdminDashboard() {
            updateDashboardStats();
            loadRecentActivity();
            loadUsersTable();
            loadOrdersTable();
        }

        function updateDashboardStats() {
            document.getElementById('total-users').textContent = users.filter(u => u.role !== 'admin').length;
            document.getElementById('total-orders').textContent = orders.length;
            document.getElementById('total-revenue').textContent = '$' + orders.reduce((sum, order) => sum + order.total, 0).toFixed(2);
            document.getElementById('total-products').textContent = '24'; // Static for demo
        }

        function loadRecentActivity() {
            // Load recent orders
            const recentOrders = orders.slice(-5).reverse();
            const recentOrdersContainer = document.getElementById('recent-orders');
            recentOrdersContainer.innerHTML = recentOrders.map(order => `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <p class="font-medium text-gray-900">#${order.id}</p>
                        <p class="text-sm text-gray-600">$${order.total.toFixed(2)}</p>
                    </div>
                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                        ${order.status}
                    </span>
                </div>
            `).join('') || '<p class="text-gray-500">No recent orders</p>';

            // Load recent users
            const recentUsers = users.filter(u => u.role !== 'admin').slice(-5).reverse();
            const recentUsersContainer = document.getElementById('recent-users');
            recentUsersContainer.innerHTML = recentUsers.map(user => `
                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">${user.name}</p>
                        <p class="text-sm text-gray-600">${user.email}</p>
                    </div>
                </div>
            `).join('') || '<p class="text-gray-500">No recent users</p>';
        }

        function showAdminSection(section) {
            // Hide all sections
            document.querySelectorAll('.admin-section').forEach(s => s.classList.add('hidden'));

            // Show selected section
            document.getElementById(section + '-section').classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('.admin-nav-btn').forEach(btn => {
                btn.classList.remove('bg-primary', 'text-white');
                btn.classList.add('text-gray-700', 'hover:bg-purple-light');
            });

            event.target.classList.add('bg-primary', 'text-white');
            event.target.classList.remove('text-gray-700', 'hover:bg-purple-light');
        }

        function loadUsersTable() {
            const usersTableBody = document.getElementById('users-table-body');
            const regularUsers = users.filter(u => u.role !== 'admin');

            usersTableBody.innerHTML = regularUsers.map(user => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${user.name}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${user.email}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            ${user.role}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${user.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(user.createdAt).toLocaleDateString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="toggleUserStatus('${user.id}')" class="text-primary hover:text-accent mr-3">
                            ${user.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button onclick="deleteUser('${user.id}')" class="text-red-600 hover:text-red-900">
                            Delete
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function loadOrdersTable() {
            const ordersTableBody = document.getElementById('orders-table-body');

            ordersTableBody.innerHTML = orders.map(order => {
                const user = users.find(u => u.id === order.userId);
                const statusColors = {
                    pending: 'bg-yellow-100 text-yellow-800',
                    processing: 'bg-blue-100 text-blue-800',
                    shipped: 'bg-purple-100 text-purple-800',
                    delivered: 'bg-green-100 text-green-800',
                    cancelled: 'bg-red-100 text-red-800'
                };

                return `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">#${order.id}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${user ? user.name : 'Unknown'}</div>
                            <div class="text-sm text-gray-500">${user ? user.email : 'N/A'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${order.items.length} items</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">$${order.total.toFixed(2)}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColors[order.status]}">
                                ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${new Date(order.createdAt).toLocaleDateString()}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <select onchange="updateOrderStatus('${order.id}', this.value)" class="text-sm border border-gray-200 rounded px-2 py-1">
                                <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>Pending</option>
                                <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>Processing</option>
                                <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>Shipped</option>
                                <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>Delivered</option>
                                <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                            </select>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function toggleUserStatus(userId) {
            const userIndex = users.findIndex(u => u.id === userId);
            if (userIndex !== -1) {
                users[userIndex].isActive = !users[userIndex].isActive;
                auth.saveUsers();
                loadUsersTable();
                showNotification(`User ${users[userIndex].isActive ? 'activated' : 'deactivated'} successfully`, 'success');
            }
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                const userIndex = users.findIndex(u => u.id === userId);
                if (userIndex !== -1) {
                    users.splice(userIndex, 1);
                    auth.saveUsers();
                    loadUsersTable();
                    updateDashboardStats();
                    showNotification('User deleted successfully', 'success');
                }
            }
        }

        function updateOrderStatus(orderId, newStatus) {
            const orderIndex = orders.findIndex(o => o.id === orderId);
            if (orderIndex !== -1) {
                orders[orderIndex].status = newStatus;
                auth.saveOrders();
                loadOrdersTable();
                showNotification('Order status updated successfully', 'success');
            }
        }

        function setupAdminEventListeners() {
            // Sidebar toggle for mobile
            document.getElementById('sidebar-toggle').addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('-translate-x-full');
            });

            // User search and filter
            document.getElementById('user-search').addEventListener('input', function() {
                filterUsers();
            });

            document.getElementById('user-filter').addEventListener('change', function() {
                filterUsers();
            });

            // Order search and filter
            document.getElementById('order-search').addEventListener('input', function() {
                filterOrders();
            });

            document.getElementById('order-status-filter').addEventListener('change', function() {
                filterOrders();
            });
        }

        function filterUsers() {
            const searchTerm = document.getElementById('user-search').value.toLowerCase();
            const filterValue = document.getElementById('user-filter').value;

            let filteredUsers = users.filter(u => u.role !== 'admin');

            if (searchTerm) {
                filteredUsers = filteredUsers.filter(user =>
                    user.name.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm)
                );
            }

            if (filterValue !== 'all') {
                if (filterValue === 'active') {
                    filteredUsers = filteredUsers.filter(user => user.isActive);
                } else if (filterValue === 'inactive') {
                    filteredUsers = filteredUsers.filter(user => !user.isActive);
                } else if (filterValue === 'admin') {
                    filteredUsers = users.filter(user => user.role === 'admin');
                }
            }

            // Update table with filtered users
            const usersTableBody = document.getElementById('users-table-body');
            usersTableBody.innerHTML = filteredUsers.map(user => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${user.name}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${user.email}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            ${user.role}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${user.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(user.createdAt).toLocaleDateString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="toggleUserStatus('${user.id}')" class="text-primary hover:text-accent mr-3">
                            ${user.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button onclick="deleteUser('${user.id}')" class="text-red-600 hover:text-red-900">
                            Delete
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function filterOrders() {
            const searchTerm = document.getElementById('order-search').value.toLowerCase();
            const statusFilter = document.getElementById('order-status-filter').value;

            let filteredOrders = orders;

            if (searchTerm) {
                filteredOrders = filteredOrders.filter(order => {
                    const user = users.find(u => u.id === order.userId);
                    return order.id.toLowerCase().includes(searchTerm) ||
                           (user && user.name.toLowerCase().includes(searchTerm)) ||
                           (user && user.email.toLowerCase().includes(searchTerm));
                });
            }

            if (statusFilter !== 'all') {
                filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
            }

            // Update table with filtered orders
            const ordersTableBody = document.getElementById('orders-table-body');
            const statusColors = {
                pending: 'bg-yellow-100 text-yellow-800',
                processing: 'bg-blue-100 text-blue-800',
                shipped: 'bg-purple-100 text-purple-800',
                delivered: 'bg-green-100 text-green-800',
                cancelled: 'bg-red-100 text-red-800'
            };

            ordersTableBody.innerHTML = filteredOrders.map(order => {
                const user = users.find(u => u.id === order.userId);
                return `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">#${order.id}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${user ? user.name : 'Unknown'}</div>
                            <div class="text-sm text-gray-500">${user ? user.email : 'N/A'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${order.items.length} items</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">$${order.total.toFixed(2)}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColors[order.status]}">
                                ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${new Date(order.createdAt).toLocaleDateString()}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <select onchange="updateOrderStatus('${order.id}', this.value)" class="text-sm border border-gray-200 rounded px-2 py-1">
                                <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>Pending</option>
                                <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>Processing</option>
                                <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>Shipped</option>
                                <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>Delivered</option>
                                <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                            </select>
                        </td>
                    </tr>
                `;
            }).join('');
        }
    </script>
</body>
</html>
