<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="products.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Products
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <a href="wishlist.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </a>
                    </div>
                    <div class="relative">
                        <a href="cart.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </a>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="hidden lg:flex items-center space-x-3 auth-buttons">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- User Menu -->
                    <div class="hidden lg:flex items-center space-x-3 user-menu" style="display: none;">
                        <div class="relative group">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-primary font-medium transition duration-300">
                                <i class="fas fa-user-circle text-xl"></i>
                                <span class="user-name">User</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-2">
                                    <a href="profile.html" class="block px-4 py-2 text-sm text-primary font-medium hover:bg-purple-light">
                                        <i class="fas fa-user mr-2"></i>My Profile
                                    </a>
                                    <a href="orders.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">
                                        <i class="fas fa-shopping-bag mr-2"></i>My Orders
                                    </a>
                                    <a href="admin.html" class="admin-link block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary" style="display: none;">
                                        <i class="fas fa-cog mr-2"></i>Admin Dashboard
                                    </a>
                                    <hr class="my-2">
                                    <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-primary font-medium">My Profile</span>
            </nav>

            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">My Profile</h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Manage your account information and preferences</p>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class="py-12 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl border border-gray-200 p-6 sticky top-24">
                        <div class="text-center mb-6">
                            <div class="w-24 h-24 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user text-white text-3xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900" id="profile-name">Loading...</h3>
                            <p class="text-gray-600" id="profile-email">Loading...</p>
                        </div>
                        
                        <nav class="space-y-2">
                            <button onclick="showProfileSection('personal')" class="profile-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 bg-primary text-white">
                                <i class="fas fa-user mr-3"></i>Personal Information
                            </button>
                            <button onclick="showProfileSection('address')" class="profile-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                                <i class="fas fa-map-marker-alt mr-3"></i>Address
                            </button>
                            <button onclick="showProfileSection('security')" class="profile-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                                <i class="fas fa-lock mr-3"></i>Security
                            </button>
                            <button onclick="showProfileSection('preferences')" class="profile-nav-btn w-full text-left px-4 py-3 rounded-lg font-medium transition-colors duration-300 text-gray-700 hover:bg-purple-light">
                                <i class="fas fa-cog mr-3"></i>Preferences
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Profile Content -->
                <div class="lg:col-span-2">
                    <!-- Personal Information Section -->
                    <div id="personal-section" class="profile-section bg-white rounded-2xl border border-gray-200 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Personal Information</h2>
                        <form id="personal-form" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                    <input type="text" id="first-name" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                    <input type="text" id="last-name" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" id="profile-email-input" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <input type="tel" id="phone" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" class="bg-primary text-white px-8 py-3 rounded-xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Address Section -->
                    <div id="address-section" class="profile-section bg-white rounded-2xl border border-gray-200 p-8 hidden">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Address Information</h2>
                        <form id="address-form" class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Street Address</label>
                                <input type="text" id="address" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                    <input type="text" id="city" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">State</label>
                                    <input type="text" id="state" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">ZIP Code</label>
                                    <input type="text" id="zipCode" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                                    <select id="country" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                        <option value="">Select Country</option>
                                        <option value="US">United States</option>
                                        <option value="CA">Canada</option>
                                        <option value="UK">United Kingdom</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" class="bg-primary text-white px-8 py-3 rounded-xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                                    Save Address
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Section -->
                    <div id="security-section" class="profile-section bg-white rounded-2xl border border-gray-200 p-8 hidden">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Security Settings</h2>
                        <form id="security-form" class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                                <input type="password" id="current-password" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                                <input type="password" id="new-password" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                                <input type="password" id="confirm-password" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" class="bg-primary text-white px-8 py-3 rounded-xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                                    Update Password
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Preferences Section -->
                    <div id="preferences-section" class="profile-section bg-white rounded-2xl border border-gray-200 p-8 hidden">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Preferences</h2>
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-semibold text-gray-900">Email Notifications</h3>
                                    <p class="text-sm text-gray-600">Receive updates about your orders and promotions</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" id="email-notifications">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-semibold text-gray-900">SMS Notifications</h3>
                                    <p class="text-sm text-gray-600">Get text messages about order updates</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" id="sms-notifications">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                </label>
                            </div>
                            <div class="flex justify-end">
                                <button onclick="savePreferences()" class="bg-primary text-white px-8 py-3 rounded-xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                                    Save Preferences
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-400">&copy; 2024 Luxe Fashion. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // Profile page specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            if (!auth.isAuthenticated()) {
                window.location.href = 'index.html';
                return;
            }

            loadProfileData();
            setupProfileEventListeners();
        });

        function loadProfileData() {
            const user = auth.getCurrentUser();
            if (user) {
                document.getElementById('profile-name').textContent = user.name;
                document.getElementById('profile-email').textContent = user.email;
                
                // Split name into first and last
                const nameParts = user.name.split(' ');
                document.getElementById('first-name').value = nameParts[0] || '';
                document.getElementById('last-name').value = nameParts.slice(1).join(' ') || '';
                document.getElementById('profile-email-input').value = user.email;
                
                // Load profile data if exists
                if (user.profile) {
                    document.getElementById('phone').value = user.profile.phone || '';
                    document.getElementById('address').value = user.profile.address || '';
                    document.getElementById('city').value = user.profile.city || '';
                    document.getElementById('state').value = user.profile.state || '';
                    document.getElementById('zipCode').value = user.profile.zipCode || '';
                    document.getElementById('country').value = user.profile.country || '';
                }
            }
        }

        function showProfileSection(section) {
            // Hide all sections
            document.querySelectorAll('.profile-section').forEach(s => s.classList.add('hidden'));
            
            // Show selected section
            document.getElementById(section + '-section').classList.remove('hidden');
            
            // Update navigation
            document.querySelectorAll('.profile-nav-btn').forEach(btn => {
                btn.classList.remove('bg-primary', 'text-white');
                btn.classList.add('text-gray-700', 'hover:bg-purple-light');
            });
            
            event.target.classList.add('bg-primary', 'text-white');
            event.target.classList.remove('text-gray-700', 'hover:bg-purple-light');
        }

        function setupProfileEventListeners() {
            // Personal information form
            document.getElementById('personal-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const firstName = document.getElementById('first-name').value;
                const lastName = document.getElementById('last-name').value;
                const email = document.getElementById('profile-email-input').value;
                const phone = document.getElementById('phone').value;
                
                const profileData = {
                    name: `${firstName} ${lastName}`.trim(),
                    email: email,
                    profile: {
                        ...auth.getCurrentUser().profile,
                        phone: phone
                    }
                };
                
                try {
                    auth.updateProfile(profileData);
                    showNotification('Profile updated successfully!', 'success');
                    loadProfileData();
                } catch (error) {
                    showNotification(error.message, 'error');
                }
            });

            // Address form
            document.getElementById('address-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const profileData = {
                    profile: {
                        ...auth.getCurrentUser().profile,
                        address: document.getElementById('address').value,
                        city: document.getElementById('city').value,
                        state: document.getElementById('state').value,
                        zipCode: document.getElementById('zipCode').value,
                        country: document.getElementById('country').value
                    }
                };
                
                try {
                    auth.updateProfile(profileData);
                    showNotification('Address updated successfully!', 'success');
                } catch (error) {
                    showNotification(error.message, 'error');
                }
            });

            // Security form
            document.getElementById('security-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;
                
                if (currentPassword !== auth.getCurrentUser().password) {
                    showNotification('Current password is incorrect', 'error');
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    showNotification('New passwords do not match', 'error');
                    return;
                }
                
                if (checkPasswordStrength(newPassword) < 2) {
                    showNotification('Please choose a stronger password', 'error');
                    return;
                }
                
                try {
                    auth.updateProfile({ password: newPassword });
                    showNotification('Password updated successfully!', 'success');
                    document.getElementById('security-form').reset();
                } catch (error) {
                    showNotification(error.message, 'error');
                }
            });
        }

        function savePreferences() {
            const emailNotifications = document.getElementById('email-notifications').checked;
            const smsNotifications = document.getElementById('sms-notifications').checked;
            
            const profileData = {
                profile: {
                    ...auth.getCurrentUser().profile,
                    emailNotifications: emailNotifications,
                    smsNotifications: smsNotifications
                }
            };
            
            try {
                auth.updateProfile(profileData);
                showNotification('Preferences saved successfully!', 'success');
            } catch (error) {
                showNotification(error.message, 'error');
            }
        }
    </script>
</body>
</html>
