<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="products.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Products
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <a href="wishlist.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </a>
                    </div>
                    <div class="relative">
                        <a href="cart.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </a>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="hidden lg:flex items-center space-x-3">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <a href="cart.html" class="hover:text-primary transition duration-300">Cart</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-primary font-medium">Checkout</span>
            </nav>

            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">Checkout</h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Complete your purchase securely</p>
            </div>
        </div>
    </section>

    <!-- Checkout Content -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Checkout Form -->
                <div class="space-y-8">
                    <!-- Contact Information -->
                    <div class="bg-white rounded-2xl border border-gray-200 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input type="email" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="<EMAIL>">
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary">
                                <label class="ml-2 text-sm text-gray-600">Email me with news and offers</label>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address -->
                    <div class="bg-white rounded-2xl border border-gray-200 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Shipping Address</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="John">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="Doe">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="123 Main Street">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Apartment, suite, etc. (optional)</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="Apt 4B">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="New York">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">State</label>
                                <select class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                    <option>Select State</option>
                                    <option>New York</option>
                                    <option>California</option>
                                    <option>Texas</option>
                                    <option>Florida</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">ZIP Code</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="10001">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                                <select class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                    <option>United States</option>
                                    <option>Canada</option>
                                    <option>United Kingdom</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="bg-white rounded-2xl border border-gray-200 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Payment Information</h2>
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Card Number</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="1234 5678 9012 3456">
                            </div>
                            <div class="grid grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Expiry Date</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="MM/YY">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="123">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Name on Card</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300" placeholder="John Doe">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="lg:sticky lg:top-24 lg:h-fit">
                    <div class="bg-gray-50 rounded-2xl p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">Order Summary</h3>

                        <!-- Order Items -->
                        <div id="checkout-items" class="space-y-4 mb-6">
                            <!-- Items will be populated by JavaScript -->
                        </div>

                        <!-- Order Totals -->
                        <div class="space-y-4 mb-8 pt-6 border-t">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="font-semibold" id="checkout-subtotal">$0.00</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Shipping</span>
                                <span class="font-semibold text-green-600">Free</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax</span>
                                <span class="font-semibold" id="checkout-tax">$0.00</span>
                            </div>
                            <div class="border-t pt-4">
                                <div class="flex justify-between">
                                    <span class="text-xl font-bold">Total</span>
                                    <span class="text-xl font-bold text-primary" id="checkout-total">$0.00</span>
                                </div>
                            </div>
                        </div>

                        <!-- Complete Order Button -->
                        <button onclick="completeOrder()" class="w-full bg-primary text-white py-4 rounded-2xl font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105 mb-4">
                            Complete Order
                        </button>

                        <!-- Security Notice -->
                        <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-lock"></i>
                            <span>Your payment information is secure and encrypted</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h4 class="text-xl font-bold text-secondary mb-4">Luxe Fashion</h4>
                    <p class="text-gray-400 mb-4">Your destination for premium fashion and timeless style.</p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Quick Links</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Size Guide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Returns</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Categories</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Women's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Men's Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Accessories</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Sale Items</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h5 class="font-semibold text-secondary mb-4">Customer Service</h5>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Shipping Info</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Track Order</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Luxe Fashion. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // Checkout-specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadCheckoutItems();
            updateCheckoutSummary();
        });

        function loadCheckoutItems() {
            const checkoutItems = document.getElementById('checkout-items');

            if (cart.length === 0) {
                checkoutItems.innerHTML = '<p class="text-gray-500 text-center">No items in cart</p>';
                return;
            }

            const itemsHTML = cart.map(item => `
                <div class="flex items-center space-x-4 p-4 bg-white rounded-xl">
                    <div class="w-16 h-16 bg-gradient-to-br from-secondary to-purple-light rounded-lg flex-shrink-0"></div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800">${item.name}</h4>
                        <p class="text-sm text-gray-600">Qty: 1</p>
                    </div>
                    <span class="font-semibold text-primary">$${item.price}</span>
                </div>
            `).join('');

            checkoutItems.innerHTML = itemsHTML;
        }

        function updateCheckoutSummary() {
            const subtotal = cart.reduce((sum, item) => sum + item.price, 0);
            const tax = subtotal * 0.08; // 8% tax
            const total = subtotal + tax;

            document.getElementById('checkout-subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('checkout-tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('checkout-total').textContent = `$${total.toFixed(2)}`;
        }

        function completeOrder() {
            // Check if user is logged in
            if (!auth.isAuthenticated()) {
                showNotification('Please log in to complete your order', 'error');
                return;
            }

            // Validate form fields
            const requiredFields = [
                'input[type="email"]',
                'input[placeholder="John"]',
                'input[placeholder="Doe"]',
                'input[placeholder="123 Main Street"]',
                'input[placeholder="New York"]',
                'select',
                'input[placeholder="10001"]',
                'input[placeholder="1234 5678 9012 3456"]',
                'input[placeholder="MM/YY"]',
                'input[placeholder="123"]',
                'input[placeholder="John Doe"]'
            ];

            let isValid = true;
            requiredFields.forEach(selector => {
                const field = document.querySelector(selector);
                if (field && !field.value.trim()) {
                    isValid = false;
                }
            });

            if (!isValid) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            if (cart.length === 0) {
                showNotification('Your cart is empty', 'error');
                return;
            }

            // Simulate order processing
            showNotification('Processing your order...', 'info');

            setTimeout(() => {
                // Create order object
                const order = {
                    id: 'ORD_' + Date.now(),
                    userId: auth.getCurrentUser().id,
                    items: cart.map(item => ({
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        quantity: item.quantity || 1
                    })),
                    subtotal: cart.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0),
                    tax: cart.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0) * 0.08,
                    total: cart.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0) * 1.08,
                    status: 'pending',
                    createdAt: new Date().toISOString(),
                    shippingAddress: {
                        firstName: document.querySelector('input[placeholder="John"]').value,
                        lastName: document.querySelector('input[placeholder="Doe"]').value,
                        address: document.querySelector('input[placeholder="123 Main Street"]').value,
                        city: document.querySelector('input[placeholder="New York"]').value,
                        state: document.querySelector('select').value,
                        zipCode: document.querySelector('input[placeholder="10001"]').value,
                        country: document.querySelectorAll('select')[1].value
                    },
                    paymentMethod: {
                        cardNumber: '****' + document.querySelector('input[placeholder="1234 5678 9012 3456"]').value.slice(-4),
                        nameOnCard: document.querySelector('input[placeholder="John Doe"]').value
                    }
                };

                // Add order to orders array
                orders.push(order);
                auth.saveOrders();

                // Clear cart
                cart = [];
                updateCartUI();
                auth.saveUserData();

                showNotification('Order placed successfully!', 'success');

                // Redirect to orders page after a delay
                setTimeout(() => {
                    window.location.href = 'orders.html';
                }, 2000);
            }, 2000);
        }
    </script>
</body>
</html>
