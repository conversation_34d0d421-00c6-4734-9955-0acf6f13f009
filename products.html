<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="products.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Products
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-md mx-8">
                    <div class="relative w-full">
                        <input type="text" placeholder="Search for products..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                               id="search-input">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <a href="wishlist.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </a>
                    </div>
                    <div class="relative">
                        <a href="cart.html" class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </a>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="hidden lg:flex items-center space-x-3 auth-buttons">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- User Menu (Hidden by default) -->
                    <div class="hidden lg:flex items-center space-x-3 user-menu" style="display: none;">
                        <div class="relative group">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-primary font-medium transition duration-300">
                                <i class="fas fa-user-circle text-xl"></i>
                                <span class="user-name">User</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <div class="py-2">
                                    <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">
                                        <i class="fas fa-user mr-2"></i>My Profile
                                    </a>
                                    <a href="orders.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary">
                                        <i class="fas fa-shopping-bag mr-2"></i>My Orders
                                    </a>
                                    <a href="admin.html" class="admin-link block px-4 py-2 text-sm text-gray-700 hover:bg-purple-light hover:text-primary" style="display: none;">
                                        <i class="fas fa-cog mr-2"></i>Admin Dashboard
                                    </a>
                                    <hr class="my-2">
                                    <button onclick="logout()" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span id="breadcrumb-category" class="text-primary font-medium">Products</span>
            </nav>

            <!-- Category Header -->
            <div class="text-center mb-12">
                <h1 id="category-title" class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">All Products</h1>
                <p id="category-description" class="text-xl text-gray-600 max-w-2xl mx-auto">Discover our complete collection of premium fashion items</p>
            </div>
        </div>
    </section>

    <!-- Product Filters and Grid -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Filter Controls -->
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">Showing</span>
                    <span class="text-gray-600"><span id="product-count">0</span> products</span>
                </div>

                <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <!-- Sort Dropdown -->
                    <div class="relative">
                        <select class="appearance-none bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl px-4 py-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 hover:bg-white hover:shadow-md cursor-pointer" id="sort-select">
                            <option value="featured">Featured</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="newest">Newest First</option>
                            <option value="rating">Highest Rated</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                        </div>
                    </div>

                    <!-- Back to Categories Button -->
                    <a href="index.html#categories" class="bg-primary text-white px-6 py-3 rounded-2xl font-medium hover:bg-accent transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to Categories</span>
                    </a>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8" id="products-grid">
                <!-- Products will be dynamically loaded here -->
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button id="load-more-btn" class="bg-white border-2 border-primary text-primary px-8 py-4 rounded-2xl font-semibold hover:bg-primary hover:text-white transition-all duration-300 transform hover:scale-105">
                    Load More Products
                </button>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script src="products.js"></script>
</body>
</html>
